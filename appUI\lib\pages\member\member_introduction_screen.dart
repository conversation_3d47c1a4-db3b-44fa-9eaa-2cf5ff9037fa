import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:milestone/utils/navigation_route.dart';
import 'package:milestone/widget/background_scaffold.dart';

import '../../generated/l10n.dart';
import '../../generated/assets.dart';
import '../../r.dart';
import '../../themes/colors.dart';
import '../../utils/user_utils.dart';
import 'member_order_payment_screen.dart';
import 'member_level.dart';

// 新的会员等级数据模型
class NewMemberLevel {
  final int id;
  final String name;
  final String icon;
  final int commissionRate;
  final int upgradeType;
  final double upgradePrice;
  final bool isAvailable;
  final String description;
  final Color color;
  final List<Color> gradientColors;

  NewMemberLevel({
    required this.id,
    required this.name,
    required this.icon,
    required this.commissionRate,
    required this.upgradeType,
    required this.upgradePrice,
    required this.isAvailable,
    required this.description,
    required this.color,
    required this.gradientColors,
  });
}

class MemberIntroductionScreen extends StatefulWidget {
  final bool showAll;
  const MemberIntroductionScreen({super.key, this.showAll = false});

  @override
  State<MemberIntroductionScreen> createState() =>
      _MemberIntroductionScreenState();
}

class _MemberIntroductionScreenState extends State<MemberIntroductionScreen> {
  PageController _pageController = PageController(initialPage: 0);
  int currentIndex = 0;
  int userCurrentLevel = 1; // 用户当前等级ID

  // 显示的5个主要等级（去掉总团用户，因为它是特殊渠道）
  final List<NewMemberLevel> levels = [
    NewMemberLevel(
      id: 1,
      name: "普通用户啊啊啊",
      icon: "",
      commissionRate: 80,
      upgradeType: 0,
      upgradePrice: 0,
      isAvailable: true,
      description: "注册即可获得",
      color: Color(0xFF9E9E9E),
      gradientColors: [Color(0xFF9E9E9E), Color(0xFF757575)],
    ),
    NewMemberLevel(
      id: 2,
      name: "银牌用户",
      icon: "🥈",
      commissionRate: 95,
      upgradeType: 1,
      upgradePrice: 10000,
      isAvailable: true,
      description: "付费购买升级",
      color: Color(0xFFC0C0C0),
      gradientColors: [Color(0xFFC0C0C0), Color(0xFF9E9E9E)],
    ),
    NewMemberLevel(
      id: 3,
      name: "金牌用户",
      icon: "🥇",
      commissionRate: 110,
      upgradeType: 2,
      upgradePrice: 0,
      isAvailable: false,
      description: "线下申请",
      color: Color(0xFFFFD700),
      gradientColors: [Color(0xFFFFD700), Color(0xFFFFA000)],
    ),
    NewMemberLevel(
      id: 4,
      name: "钻石用户",
      icon: "💎",
      commissionRate: 125,
      upgradeType: 2,
      upgradePrice: 0,
      isAvailable: false,
      description: "线下申请",
      color: Color(0xFF00BCD4),
      gradientColors: [Color(0xFF00BCD4), Color(0xFF0097A7)],
    ),
    NewMemberLevel(
      id: 5,
      name: "王者用户",
      icon: "👑",
      commissionRate: 140,
      upgradeType: 2,
      upgradePrice: 0,
      isAvailable: false,
      description: "线下申请",
      color: Color(0xFF9C27B0),
      gradientColors: [Color(0xFF9C27B0), Color(0xFF7B1FA2)],
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return BackgroundScaffold(
      appBar: AppBar(
        systemOverlayStyle: SystemUiOverlayStyle.light,
        elevation: 0,
        backgroundColor: Colors.transparent,
        centerTitle: true,
        leading: IconButton(
          onPressed: () {
            Navigator.pop(context);
          },
          icon: Image.asset(Assets.imagesIcArrowBack, color: Colors.white),
        ),
        title: Text(
          S.of(context).member_level_state,
          style: TextStyle(
            fontSize: 15,
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
        ),
      ),
      bottomNavigationBar: _buildUpgradeButton(context),
      backgroundImage: R.assetsImagesBgMemberIntro,
      backgroundColor: backgroundColor,
      title: S.of(context).member_level_state,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 用户信息区域
          _buildUserContent(context),

          SizedBox(height: 16),

          // 会员等级选择标签栏
          _buildMemberLevelTabBar(context),

          SizedBox(height: 16),

          // 等级卡片和权益的PageView
          Expanded(
            child: PageView.builder(
              controller: _pageController,
              onPageChanged: (index) {
                setState(() {
                  currentIndex = index;
                });
              },
              itemCount: levels.length,
              itemBuilder: (context, index) {
                return _buildLevelPage(levels[index]);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUserContent(BuildContext context) {
    double imageWidth = 44;
    return Container(
      padding: EdgeInsets.only(top: 12, left: 12, right: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          ClipOval(
            child: Container(
              width: imageWidth,
              height: imageWidth,
              color: Colors.white.withValues(alpha: 0.2),
              child: Center(
                child: Text(
                  levels[userCurrentLevel - 1].icon,
                  style: TextStyle(fontSize: 20),
                ),
              ),
            ),
          ),
          SizedBox(width: 10),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                UserManager.getNickname() ?? "----",
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: fourthTextColor,
                ),
              ),
              SizedBox(height: 4),
              Text(
                S.of(context).member_status_description,
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                  color: fourthTextColor,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildMemberLevelTabBar(BuildContext context) {
    return Stack(
      alignment: Alignment.center,
      children: [
        Image.asset(
          levels[currentIndex].id <= 2
              ? R.assetsImagesBgMemberTabLeft
              : R.assetsImagesBgMemberTabRight,
          fit: BoxFit.cover,
          width: MediaQuery.of(context).size.width,
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: levels.map((level) {
            bool isSelected = level.id == levels[currentIndex].id;
            return Expanded(
              child: InkWell(
                onTap: () {
                  setState(() {
                    currentIndex = level.id - 1;
                  });
                  _pageController.animateToPage(
                    level.id - 1,
                    duration: Duration(milliseconds: 300),
                    curve: Curves.easeInOut,
                  );
                },
                child: Container(
                  height: 42,
                  child: Center(
                    child: Text(
                      level.name,
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                        color: isSelected
                            ? Color(0xFFB76928)
                            : Colors.white,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildLevelPage(NewMemberLevel level) {
    return Column(
      children: [
        // 等级卡片
        _buildMemberLevelCard(level),

        SizedBox(height: 16),

        // 专属权益标题
        Container(
          padding: EdgeInsets.symmetric(vertical: 12, horizontal: 16),
          child: Text(
            S.of(context).exclusive_benefits,
            style: TextStyle(
              color: primaryTextColor,
              fontSize: 14,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),

        // 权益列表
        Expanded(
          child: _buildMemberBenefitsList(level),
        ),
      ],
    );
  }

  Widget _buildMemberLevelCard(NewMemberLevel level) {
    bool isCurrentLevel = level.id == userCurrentLevel;

    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16),
      child: Stack(
        children: [
          Image.asset(
            _getLevelBackgroundImage(level),
            width: MediaQuery.of(context).size.width,
            fit: BoxFit.cover,
          ),
          Container(
            margin: EdgeInsets.symmetric(vertical: 25, horizontal: 16),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  level.name,
                  style: TextStyle(
                    color: Color(0xFF415060),
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    fontStyle: FontStyle.italic,
                  ),
                ),
                Text(
                  level.description,
                  style: TextStyle(
                    color: Color(0xFF415060),
                    fontSize: 12,
                    fontWeight: FontWeight.w400,
                  ),
                ),
                if (level.upgradePrice > 0)
                  Text(
                    "升级费用: Rp ${level.upgradePrice.toStringAsFixed(0)}",
                    style: TextStyle(
                      color: Color(0xFF415060),
                      fontSize: 12,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                SizedBox(height: 25),
                _buildCommissionDisplay(level, isCurrentLevel),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCommissionDisplay(NewMemberLevel level, bool isCurrentLevel) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              "佣金比例",
              style: TextStyle(
                color: Color(0xFF415060),
                fontSize: 12,
                fontWeight: FontWeight.w400,
              ),
            ),
            Text(
              "${level.commissionRate}%",
              style: TextStyle(
                color: Color(0xFF415060),
                fontSize: 12,
                fontWeight: FontWeight.w400,
              ),
            ),
          ],
        ),
        SizedBox(height: 6),
        Container(
          height: 8,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(4),
            gradient: LinearGradient(
              colors: level.gradientColors,
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
        ),
        SizedBox(height: 6),
        Text(
          isCurrentLevel ? "您的当前等级" : "升级后可享受此比例",
          style: TextStyle(
            color: Color(0xFF415060),
            fontSize: 12,
            fontWeight: FontWeight.w400,
          ),
        ),
      ],
    );
  }

  Widget _buildMemberBenefitsList(NewMemberLevel level) {
    List<String> benefits = _getLevelBenefits(level);

    return ListView.builder(
      physics: const BouncingScrollPhysics(),
      itemCount: benefits.length,
      itemBuilder: (context, index) {
        return Container(
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
          margin: const EdgeInsets.symmetric(vertical: 6, horizontal: 12),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(15),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 4,
                offset: Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [Color(0xFFFFFEE8), Color(0xFFECC8AF)],
                  ),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Center(
                  child: Icon(
                    Icons.star,
                    color: Color(0xFFB76928),
                    size: 20,
                  ),
                ),
              ),
              const SizedBox(width: 14),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      benefits[index],
                      style: TextStyle(
                        color: primaryTextColor,
                        fontWeight: FontWeight.w600,
                        fontSize: 13,
                        height: 1.2,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  List<String> _getLevelBenefits(NewMemberLevel level) {
    switch (level.id) {
      case 1:
        return [
          "基础佣金返现 80%",
          "新手指导培训",
          "在线客服支持",
          "基础推广工具",
          "产品培训资料",
          "社群交流平台",
        ];
      case 2:
        return [
          "提升佣金比例至 95%",
          "专属客服支持",
          "高级培训资料",
          "月度奖励计划",
          "推广素材库",
          "数据分析报告",
          "优先客服响应",
        ];
      case 3:
        return [
          "提升佣金比例至 110%",
          "VIP客服通道",
          "专业培训课程",
          "季度奖励计划",
          "团队管理工具",
          "专属推广链接",
          "高级数据分析",
          "月度业绩奖金",
        ];
      case 4:
        return [
          "提升佣金比例至 125%",
          "专属客户经理",
          "高级管理培训",
          "月度现金奖励",
          "团队建设支持",
          "优先产品推荐",
          "独家营销工具",
          "区域推广权限",
          "年度表彰奖励",
        ];
      case 5:
        return [
          "提升佣金比例至 140%",
          "一对一专属服务",
          "领导力培训",
          "丰厚现金奖励",
          "区域管理权限",
          "独家产品渠道",
          "年度表彰大会",
          "海外培训机会",
          "股权激励计划",
          "品牌合作机会",
        ];
      default:
        return [
          "基础权益",
          "标准服务",
          "常规支持",
        ];
    }
  }

  String _getLevelBackgroundImage(NewMemberLevel level) {
    switch (level.id) {
      case 1:
        return R.assetsImagesBgMemberNormal;
      case 2:
        return R.assetsImagesBgMemberAgentSilver;
      case 3:
        return R.assetsImagesBgMemberAgentGold;
      case 4:
        return R.assetsImagesBgMemberAgentDiamand;
      case 5:
        return R.assetsImagesBgMemberPartnerGold;
      case 6:
        return R.assetsImagesBgMemberPartnerDiamand;
      default:
        return R.assetsImagesBgMemberNormal;
    }
  }

  Widget? _buildUpgradeButton(BuildContext context) {
    NewMemberLevel currentLevel = levels[currentIndex];
    bool canUpgrade = currentLevel.id == userCurrentLevel + 1 && currentLevel.isAvailable;

    if (!canUpgrade) return null;

    return TextButton(
      onPressed: () {
        // 跳转到升级支付页面
        customRouter(
          context,
          MemberOrderPaymentScreen(memberLevel: MemberLevel.silverAgent),
        );
      },
      child: Container(
        height: 46,
        margin: EdgeInsets.only(
          left: 16,
          right: 16,
          top: 6,
          bottom: 20 + MediaQuery.of(context).padding.bottom
        ),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Color(0xFFC80D1F), Color(0xFFFB4143)],
          ),
          borderRadius: BorderRadius.circular(15),
        ),
        child: Center(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                "立即升级 - Rp ${currentLevel.upgradePrice.toStringAsFixed(0)}",
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
